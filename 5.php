<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二要素验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background: url('https://www.loliapi.com/acg/') no-repeat center center fixed;
            background-size: cover;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        input, button, textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 10px;
            font-size: 16px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        textarea {
            height: 150px;
        }
        #message {
            color: red;
        }

















        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(255, 0, 0, 0.8); /* 半透明红色背景 */
            color: white;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            padding: 10px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        }


















    </style>
</head>
<body>
    <div class="container">
        <h2>二要素验证</h2>
        <form method="POST">
            <input type="text" name="kami" placeholder="请输入卡密" required>
            <input type="text" name="name" placeholder="请输入姓名" required>
            <input type="text" name="number" placeholder="请输入身份证号" required>
            <button type="submit">查询</button>
        </form>
        <div id="message">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $kami = $_POST['kami'];
                $name = $_POST['name'];
                $number = $_POST['number'];
                $file = 'kami.txt';

                if (file_exists($file)) {
                    $content = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                    if (in_array($kami, $content)) {
                        echo '卡密使用成功, 正在查询...<br>';

                        // Remove the used kami from the file
                        $updatedContent = array_diff($content, [$kami]);
                        file_put_contents($file, implode(PHP_EOL, $updatedContent) . PHP_EOL);

                        // Call the API
                        $url = "http://127.0.0.1:3001/2ys.php?token=gongyi&name=$name&number=$number";
                        $response = file_get_contents($url);
                        $json = json_decode($response, true);

                        if ($json) {
                            foreach ($json as $key => $value) {
                                echo htmlspecialchars("$key: $value") . "<br>";
                            }
                        } else {
                            echo '查询失败，请稍后再试。';
                        }
                    } else {
                        echo '卡密不存在或已使用';
                    }
                } else {
                    echo '卡密文件不存在';
                }
            }
            ?>
        </div>
    </div>

    <div class="footer">
    欢迎使用,高级社工查询系统
    </div>

</body>
</html>
