<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学籍查询</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background: url('https://www.loliapi.com/acg/') no-repeat center center fixed;
            background-size: cover;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        input, button, textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 10px;
            font-size: 16px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        textarea {
            height: 150px;
        }
        #message {
            color: red;
        }













        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(255, 0, 0, 0.8); /* 半透明红色背景 */
            color: white;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            padding: 10px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        }

















    </style>
</head>
<body>
    <div class="container">
        <h2>学籍查询</h2>
        <form method="POST">
            <input type="text" name="kami" placeholder="输入卡密" required>
            <input type="text" name="query" placeholder="可输入姓名/身份证号/手机号/学校" required>
            <button type="submit">查询</button>
        </form>
        <textarea id="message" readonly><?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $kami = trim($_POST['kami']);
                $query = trim($_POST['query']);
                
                if (strlen($query) > 1) {
                    $file = 'kami.txt';
                    $kami_list = file($file, FILE_IGNORE_NEW_LINES);
                    
                    if (in_array($kami, $kami_list)) {
                        echo "卡密使用成功，正在查询...\n";
                        
                        // Remove the used kami from the file
                        $new_kami_list = array_filter($kami_list, function($item) use ($kami) {
                            return $item !== $kami;
                        });
                        file_put_contents($file, implode("\n", $new_kami_list));
                        
                        // Call the API
                        $url = "https://4a8b020c.r23.cpolar.top/xueji.php?token=gongyi&text=" . urlencode($query);
                        $response = file_get_contents($url);
                        $data = json_decode($response, true);
                        
                        // Output the response as plain text
                        if ($data) {
                            foreach ($data as $key => $value) {
                                echo ucfirst($key) . ": " . $value . "\n";
                            }
                        } else {
                            echo "查询失败，无法解析响应内容。";
                        }
                    } else {
                        echo "卡密不存在或已使用";
                    }
                } else {
                    echo "查询信息格式不正确";
                }
            }
        ?></textarea>
    </div>

    <div class="footer">
    欢迎使用,高级社工查询系统
    </div>

</body>
</html>
