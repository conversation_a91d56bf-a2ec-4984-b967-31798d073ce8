<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>Q绑查询</title>
<style>
    body {
        font-family: Arial, sans-serif;
        text-align: center;
        background: url('https://www.loliapi.com/acg/') no-repeat center center fixed;
        background-size: cover;
        margin: 0;
        padding: 0;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .container {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 30px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    input, button, textarea {
        width: 100%;
        padding: 10px;
        margin-bottom: 10px;
        box-sizing: border-box;
        border: 1px solid #ccc;
        border-radius: 10px;
        font-size: 16px;
    }
    button {
        background-color: #4CAF50;
        color: white;
        border: none;
        cursor: pointer;
    }
    button:hover {
        background-color: #45a049;
    }
    textarea {
        height: 150px;
    }
    #message {
        color: red;
    }













    .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(255, 0, 0, 0.8); /* 半透明红色背景 */
            color: white;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            padding: 10px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        }



















</style>
</head>
<body>
<div class="container">
    <h2>Q绑查询</h2>
    <form method="post">
        <input type="text" name="kami" placeholder="请输入卡密（大于5位）">
        <input type="text" name="qq" placeholder="请输入QQ号">
        <p id="message">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $kami = trim($_POST['kami']);
                $qq = trim($_POST['qq']);
                if (strlen($kami) < 5) {
                    echo '请输入正确的卡密（大于5位）';
                } else {
                    $kamis = file('kami.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                    $kamiFound = false;
                    $updatedKamis = [];

                    foreach ($kamis as $line) {
                        if (trim($line) === $kami) {
                            $kamiFound = true;
                        } else {
                            $updatedKamis[] = $line;
                        }
                    }

                    if (!$kamiFound) {
                        $output = '输入的卡密不存在或已使用';
                    } else {
                        file_put_contents('kami.txt', implode("\n", $updatedKamis));

                        $apiUrl = 'http://boke.huoshen.icu/API888/qb.php?qq=' . urlencode($qq);
                        $output = file_get_contents($apiUrl);

                        if ($output === FALSE) {
                            $output = '查询出错：无法访问API';
                        }
                    }
                }
            }
            ?>
        </p>
        <button type="submit">查询</button>
        <textarea id="output" placeholder="查询结果将显示在这里" readonly><?php echo isset($output) ? $output : ''; ?></textarea>
    </form>
</div>
<div class="footer">
        欢迎使用,高级社工查询系统
    </div>
</body>
</html>
