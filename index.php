<?php
// 读取信息.txt文件内容
$fileContent = file_get_contents('信息.txt');

// 提取标题、公告和客服信息
preg_match('/标题【(.*?)】标题/', $fileContent, $titleMatches);
preg_match('/公告【(.*?)】公告/', $fileContent, $announcementMatches);
preg_match('/客服【(.*?)】客服/', $fileContent, $customerServiceMatches);

$title = $titleMatches[1];
$announcement = str_replace('\n', '<br>', $announcementMatches[1]);
$customerService = $customerServiceMatches[1];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: url('https://www.loliapi.com/acg/pe/') no-repeat center center fixed;
            background-size: cover;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #ffffff;
        }
        .container {
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        .container h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #ff4500;
        }
        .announcement h2, .customer-service h2 {
            color: #32cd32;
        }
        .announcement p, .customer-service p {
            color: #87ceeb;
        }
        .buttons {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        .buttons a {
            background: rgba(255, 69, 0, 0.8);
            backdrop-filter: blur(5px);
            border-radius: 10px;
            padding: 10px 20px;
            color: #ffffff;
            text-decoration: none;
            margin: 5px;
            flex: 1;
            min-width: 100px;
            text-align: center;
            transition: background 0.3s ease;
        }
        .buttons a:hover {
            background: rgba(255, 165, 0, 0.8);
        }
        @media (max-width: 600px) {
            .buttons a {
                flex: 1 1 45%;
            }
        }
        










        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(255, 0, 0, 0.8); /* 半透明红色背景 */
            color: white;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            padding: 10px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        }















    </style>
</head>
<body>
    
    <div class="container">
        <h1><?php echo $title; ?></h1>
        <div class="announcement">
            <h2>公告</h2>
            <p><?php echo $announcement; ?></p>
        </div>
        <div class="customer-service">
            <h2>客服</h2>
            <p>QQ: <?php echo $customerService; ?></p>
        </div>
        <div class="buttons">
            <a href="./1.php">Q绑查询</a>
            <a href="./2.php">全国信息</a>
            <a href="./3.php">人口猎魔</a>
            <a href="./4.php">学籍查询</a>
            <a href="./5.php">二要素验证</a>

        </div>
    </div>
    <div class="footer">
    欢迎使用,高级社工查询系统
    </div>
</body>
</html>
