<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            width: 300px;
            text-align: center;
        }
        .output {
            margin: 10px 0;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 10px;
            height: 100px;
            overflow-y: auto;
        }
        select, button {
            margin: 10px 0;
            padding: 10px;
            width: 100%;
            border-radius: 10px;
            border: 1px solid #ddd;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }











        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(255, 0, 0, 0.8); /* 半透明红色背景 */
            color: white;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            padding: 10px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        }


















    </style>
</head>
<body>

<div class="container">
    <h1>卡密生成器</h1>
    <form method="post">
        <select name="quantity">
            <option value="1">1 张</option>
            <option value="5">5 张</option>
            <option value="10">10 张</option>
            <option value="20">20 张</option>
        </select>
        <button type="submit" name="generate">生成卡密</button>
    </form>
    <div class="output">
        <h3>现有卡密</h3>
        <?php
        $kamiFile = 'kami.txt';
        if (file_exists($kamiFile)) {
            echo nl2br(file_get_contents($kamiFile));
        } else {
            echo "没有现有卡密。";
        }
        ?>
    </div>
    <div class="output">
        <h3>新增卡密</h3>
        <?php
        if (isset($_POST['generate'])) {
            $quantity = intval($_POST['quantity']);
            $newKamis = generateKamis($quantity);
            echo nl2br(implode("\n", $newKamis));

            // Append new KAMIs to the file
            file_put_contents($kamiFile, implode("\n", $newKamis) . "\n", FILE_APPEND);
        }

        function generateKamis($quantity) {
            $kamis = [];
            $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            $charactersLength = strlen($characters);
            for ($i = 0; $i < $quantity; $i++) {
                $kami = '';
                for ($j = 0; $j < 15; $j++) {
                    $kami .= $characters[rand(0, $charactersLength - 1)];
                }
                $kamis[] = $kami;
            }
            return $kamis;
        }
        ?>
    </div>
</div>
<div class="footer">
欢迎使用,高级社工查询系统
    </div>
</body>
</html>
