<?php
// 定义文件路径
$file_path = '信息.txt';

// 初始化变量
$title = '';
$announcement = '';
$customer_service = '';

// 读取文件内容
if (file_exists($file_path)) {
    $file_contents = file($file_path, FILE_IGNORE_NEW_LINES);
    foreach ($file_contents as $line) {
        if (strpos($line, '标题') !== false) {
            $title = str_replace(['标题【', '】标题'], '', $line);
        } elseif (strpos($line, '公告') !== false) {
            $announcement = str_replace(['公告【', '】公告'], '', $line);
        } elseif (strpos($line, '客服') !== false) {
            $customer_service = str_replace(['客服【', '】客服'], '', $line);
        }
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'];
    $announcement = $_POST['announcement'];
    $customer_service = $_POST['customer_service'];

    // 更新文件内容
    $new_contents = "标题【{$title}】标题\n公告【{$announcement}】公告\n客服【{$customer_service}】客服";
    file_put_contents($file_path, $new_contents);
    
    echo "内容已保存!";
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑信息内容</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-image: url('https://www.dmoe.cc/random.php'); /* 背景图片 */
            background-size: cover;
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            width: 100%;
            max-width: 500px;
        }
        h1 {
            text-align: center;
            color: #fff;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
            color: #fff;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin-top: 5px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.8);
            color: #333;
        }
        textarea {
            height: 100px;
        }
        button {
            display: block;
            width: 100%;
            padding: 10px;
            margin-top: 20px;
            background-color: rgba(0, 123, 255, 0.8);
            color: #fff;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: rgba(0, 123, 255, 1);
        }













        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(255, 0, 0, 0.8); /* 半透明红色背景 */
            color: white;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            padding: 10px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        }

















    </style>
</head>
<body>
    <div class="container">
        <h1>KMH定制版后台</h1>
        <form method="post">
            <label for="title">标题</label>
            <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($title); ?>">
            
            <label for="announcement">公告</label>
            <textarea id="announcement" name="announcement"><?php echo htmlspecialchars($announcement); ?></textarea>
            
            <label for="customer_service">客服</label>
            <input type="text" id="customer_service" name="customer_service" value="<?php echo htmlspecialchars($customer_service); ?>">
            
            <button type="submit">保存</button>
        </form>
    </div>


    <div class="footer">
    欢迎使用,高级社工查询系统
    </div>

</body>
</html>
